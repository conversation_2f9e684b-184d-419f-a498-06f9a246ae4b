<div class="category-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <!-- Validation Errors -->
    <div *ngIf="hasValidationErrors" class="validation-errors">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-list">
        <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
      </div>
    </div>

    <!-- Two Panel Layout -->
    <div class="two-panel-layout">
      <!-- Category Selection Section (Left Panel) -->
      <div class="category-selection-section">
        <div class="section-header">
          <mat-icon>category</mat-icon>
          <h3>Select Categories</h3>
        </div>

        <!-- Loading State -->
        <div *ngIf="categories.length === 0" class="loading-state">
          <mat-spinner diameter="24"></mat-spinner>
          <p>Loading categories...</p>
        </div>

        <!-- Category Selection -->
        <mat-form-field *ngIf="categories.length > 0" appearance="outline" class="category-filter-field">
          <mat-select [formControl]="selectedCategoriesCtrl" multiple
            [placeholder]="'Select categories (' + (selectedCategoriesCtrl.value?.length || 0) + '/' + filteredCategories.length + ')'">
            <mat-option>
              <ngx-mat-select-search [formControl]="categoryFilterCtrl" placeholderLabel="Search categories..."
                noEntriesFoundLabel="No categories found">
              </ngx-mat-select-search>
            </mat-option>
            <!-- Select All / Deselect All Options -->
            <div class="select-all-custom-option" (click)="toggleAllCategories($event)">
              <strong>{{areAllCategoriesSelected() ? 'Deselect All' : 'Select All'}}</strong>
            </div>
            <mat-divider></mat-divider>
            <mat-option *ngFor="let category of filteredCategories; trackBy: trackByCategory" [value]="category">
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- WorkArea Configuration Section (Right Panel) -->
      <div class="workarea-configuration-section">
        <div class="section-header">
          <mat-icon>work</mat-icon>
          <h3>Configure Work Area Mappings as per Inventory</h3>
        </div>

        <!-- Form -->
        <form [formGroup]="mappingForm" class="mapping-form">
          <div formArrayName="mappings" class="mappings-container">
            <!-- Empty State Message -->
            <div *ngIf="mappingsFormArray.controls.length === 0" class="empty-state-message">
              <mat-icon class="empty-icon">arrow_back</mat-icon>
              <p>Select categories from the left to configure work area mappings</p>
            </div>

            <!-- Mapping Rows -->
            <div
              *ngFor="let mappingGroup of mappingsFormArray.controls; let i = index; trackBy: trackByCategoryName"
              [formGroupName]="i"
              class="mapping-row"
            >
              <div class="category-name">
                <mat-icon class="category-icon">category</mat-icon>
                <span>{{ mappingGroup.get('categoryName')?.value || 'Unknown Category' }}</span>
              </div>

              <div class="workareas-dropdown">
                <mat-form-field appearance="outline" class="workareas-field">
                  <mat-select
                    formControlName="workAreas"
                    multiple
                    [placeholder]="getWorkAreasPlaceholder(i)"
                    (selectionChange)="onWorkAreasChange(i, $event.value)"
                    [disabled]="allWorkAreas.length === 0"
                  >
                    <mat-option
                      *ngFor="let workArea of getAvailableWorkAreas(i); trackBy: trackByWorkArea"
                      [value]="workArea"
                    >
                      {{ workArea }}
                    </mat-option>
                  </mat-select>
                  <mat-hint *ngIf="allWorkAreas.length === 0">No work areas available</mat-hint>
                </mat-form-field>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Summary Section -->
    <div *ngIf="mappings.length > 0" class="mapping-summary">
      <h3>Current Mappings Summary</h3>
      <div class="summary-grid">
        <div *ngFor="let mapping of mappings" class="summary-item">
          <div class="summary-category">{{ mapping.categoryName }}</div>
          <div class="summary-workareas">
            <span class="workarea-count">{{ mapping.workAreas.length }} work areas</span>
            <div class="workarea-list">
              {{ mapping.workAreas.join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button
      mat-button
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>

    <button
      mat-raised-button
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>
  </div>
</div>
