// ===== MATERIAL DESIGN THEME OVERRIDES =====
::ng-deep {
  // Form fields
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 36px;
      min-height: 36px;
    }

    .mat-mdc-form-field-infix {
      padding: 6px 12px;
      min-height: 24px;
      border-top: none;
    }

    .mat-mdc-form-field-flex {
      align-items: center;
      height: 36px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-outline {
      color: #dee2e6;
    }

    .mat-mdc-form-field-outline-thick {
      color: #ffb366;
    }

    .mat-mdc-form-field-label {
      color: #666;
      font-size: 13px;
      top: 18px;
    }

    &.mat-focused .mat-mdc-form-field-label {
      color: #ffb366;
    }

    &.mat-form-field-should-float .mat-mdc-form-field-label {
      transform: translateY(-12px) scale(0.75);
    }
  }

  // Select dropdowns
  .mat-mdc-select {
    .mat-mdc-select-trigger {
      height: 36px;
      display: flex;
      align-items: center;
    }

    .mat-mdc-select-value {
      font-size: 13px;
      line-height: 24px;
    }

    .mat-mdc-select-arrow {
      color: #ffb366;
    }
  }

  // Select panel
  .mat-mdc-select-panel .mat-mdc-option {
    height: 32px;
    line-height: 32px;
    font-size: 13px;
    padding: 0 16px;

    &.mat-mdc-option-active {
      background: rgba(255, 179, 102, 0.1);
      color: #ffb366;
    }

    &:hover {
      background: rgba(255, 179, 102, 0.05);
    }
  }

  // Input elements
  .mat-mdc-input-element {
    font-size: 13px;
    height: 24px;
    line-height: 24px;
  }

  // Buttons
  .mat-mdc-raised-button,
  .mat-mdc-outlined-button {
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    font-size: 13px;

    &.mat-primary {
      background-color: #ffb366;
      color: white;

      &:hover {
        background-color: #ffa64d;
      }
    }
  }

  .mat-mdc-outlined-button.mat-primary {
    border-color: #ffb366;
    color: #ffb366;
    background-color: transparent;

    &:hover {
      background-color: rgba(255, 179, 102, 0.05);
    }
  }
}

.category-mapping-container {
  padding: 0;
  max-width: none;
  margin: 0;
  background: transparent;
  min-height: 300px;
  height: 100%;
  overflow: visible;
  display: flex;
  flex-direction: column;

  &.dialog-mode {
    padding: 16px;
    max-width: 850px;
    overflow: visible;
  }
}

// Loading State
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  flex: 1;
}

// Main Content
.mapping-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: visible;
}

// Two Panel Layout
.two-panel-layout {
  display: flex;
  flex: 1;
  min-height: 400px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

// Category Selection Panel (Left)
.category-selection-section {
  flex: 0 0 300px;
  border-right: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    mat-icon {
      color: #ffb366;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .category-filter-field {
    padding: 16px 20px;
    margin: 0;

    .mat-mdc-form-field {
      width: 100%;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    gap: 12px;

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
}

// Select All/Deselect All Custom Option
.select-all-custom-option {
  padding: 8px 16px;
  cursor: pointer;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #e9ecef;
  }

  strong {
    color: #ffb366;
    font-size: 13px;
    font-weight: 500;
  }
}

// WorkArea Configuration Panel (Right)
.workarea-configuration-section {
  flex: 1;
  display: flex;
  flex-direction: column;

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    mat-icon {
      color: #ffb366;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .mapping-form {
    flex: 1;
    padding: 16px 20px;
    overflow-y: auto;
  }

  .mappings-container {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .empty-state-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      text-align: center;
      color: #666;

      .empty-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #ccc;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }
}

// Validation Errors
.validation-errors {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 4px;
  margin-bottom: 16px;

  .error-icon {
    color: #e53e3e;
    font-size: 18px;
    width: 18px;
    height: 18px;
    margin-top: 1px;
  }

  .error-list {
    flex: 1;

    .error-message {
      margin: 0;
      color: #e53e3e;
      font-size: 13px;
      line-height: 1.4;

      &:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
}

// Mapping Row Styles (within right panel)
.mapping-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #ffb366;
    box-shadow: 0 1px 4px rgba(255, 179, 102, 0.1);
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 200px;
    font-weight: 500;
    color: #333;

    .category-icon {
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    span {
      font-size: 14px;
    }
  }

  .workareas-dropdown {
    flex: 1;
    margin-left: 16px;

    .workareas-field {
      width: 100%;
      margin: 0;
    }
  }
}



// Mapping Row Styles
.mapping-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #ffb366;
    box-shadow: 0 1px 4px rgba(255, 179, 102, 0.1);
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 200px;
    font-weight: 500;
    color: #333;

    .category-icon {
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    span {
      font-size: 14px;
    }
  }

  .workareas-dropdown {
    flex: 1;
    margin-left: 16px;

    .workareas-field {
      width: 100%;
      margin: 0;
    }
  }
}

// Summary Section
.mapping-summary {
  margin-top: 32px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .summary-grid {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .summary-item {
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #dee2e6;

    .summary-category {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .summary-workareas {
      .workarea-count {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      .workarea-list {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
}

// Actions Section
.mapping-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
  margin-top: 20px;
  border-top: 1px solid #e9ecef;

  .cancel-button {
    color: #666;
    border-color: #dee2e6;

    &:hover {
      background-color: #f8f9fa;
      border-color: #adb5bd;
    }
  }

  .save-button {
    position: relative;
    min-width: 120px;

    .button-spinner {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    &:disabled {
      background-color: #e9ecef;
      color: #6c757d;
      cursor: not-allowed;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .category-mapping-container {
    padding: 12px;

    &.dialog-mode {
      padding: 12px;
    }
  }

  .two-panel-layout {
    flex-direction: column;
    min-height: auto;

    .category-selection-section {
      flex: none;
      border-right: none;
      border-bottom: 1px solid #e9ecef;

      .category-filter-field {
        padding: 12px 16px;
      }
    }

    .workarea-configuration-section {
      .mapping-form {
        padding: 12px 16px;
      }
    }
  }

  .mapping-row {
    flex-direction: column;
    align-items: stretch;
    padding: 12px;
    gap: 12px;

    .category-name {
      flex: none;
      justify-content: flex-start;
    }

    .workareas-dropdown {
      margin-left: 0;
    }
  }

  .mapping-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      width: 100%;
    }
  }

  .mapping-summary {
    .summary-grid {
      grid-template-columns: 1fr;
    }
  }
}
